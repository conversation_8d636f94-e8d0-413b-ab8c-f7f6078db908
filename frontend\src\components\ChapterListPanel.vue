<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">📝</span>
        <span>章节列表</span>
      </div>
      <el-dropdown @command="handleChapterCommand">
        <el-button size="small" type="primary">
          <el-icon><Plus /></el-icon>
          新增章节 <el-icon><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="manual">手动创建</el-dropdown-item>
            <el-dropdown-item command="ai-single">AI生成单章</el-dropdown-item>
            <el-dropdown-item command="ai-batch">AI批量生成</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div
      class="chapters-list"
      v-loading="loading"
      element-loading-text="正在加载章节..."
      ref="chaptersContainer"
      @scroll="handleScroll"
    >
      <div
        v-for="(chapter, index) in chapters"
        :key="chapter.id"
        class="chapter-item"
        :class="{ active: currentChapter?.id === chapter.id }"
        @click="selectChapter(chapter)"
      >
        <div class="chapter-info">
          <h4>第{{ index + 1 }}章</h4>
          <p>{{ chapter.title }}</p>
          <div class="chapter-meta">
            <span>{{ chapter.wordCount || 0 }}字</span>
            <el-tag v-if="chapter.status" :type="getChapterStatusType(chapter.status)" size="small">
              {{ getChapterStatusText(chapter.status) }}
            </el-tag>
          </div>
          <el-tooltip
            v-if="chapter.description"
            :content="chapter.description"
            placement="top-start"
            :disabled="chapter.description.length <= 50"
            effect="light"
            :show-after="300"
          >
            <p class="chapter-desc chapter-desc-truncated">
              {{ chapter.description.length > 50 ? chapter.description.substring(0, 50) + '...' : chapter.description }}
            </p>
          </el-tooltip>
        </div>
        <div class="chapter-actions">
          <el-dropdown @command="(cmd) => handleChapterAction(cmd, chapter)">
            <el-button size="small" type="text">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                <el-dropdown-item command="generate">AI生成正文</el-dropdown-item>
                <el-dropdown-item divided command="delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 加载更多指示器 -->
      <div v-if="loadingMore" class="loading-more">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载更多章节...</span>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="!hasMore && chapters.length > 0" class="no-more-data">
        <span>已加载全部章节</span>
      </div>

      <div v-if="chapters.length === 0 && !loading" class="empty-chapters">
        <p>暂无章节</p>
        <el-button size="small" type="primary" @click="addNewChapter">
          创建第一章
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, nextTick } from 'vue'
import { Plus, ArrowDown, MoreFilled, Loading } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  chapters: {
    type: Array,
    default: () => []
  },
  currentChapter: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingMore: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'select-chapter',
  'chapter-command',
  'chapter-action',
  'add-new-chapter',
  'load-more'
])

// Refs
const chaptersContainer = ref(null)

// 方法
const selectChapter = (chapter) => {
  emit('select-chapter', chapter)
}

const handleChapterCommand = (command) => {
  emit('chapter-command', command)
}

const handleChapterAction = (command, chapter) => {
  emit('chapter-action', command, chapter)
}

const addNewChapter = () => {
  emit('add-new-chapter')
}

// 滚动处理
const handleScroll = () => {
  if (!chaptersContainer.value || props.loading || props.loadingMore || !props.hasMore) {
    return
  }

  const container = chaptersContainer.value
  const scrollTop = container.scrollTop
  const scrollHeight = container.scrollHeight
  const clientHeight = container.clientHeight

  // 当滚动到距离底部50px时触发加载更多
  if (scrollTop + clientHeight >= scrollHeight - 50) {
    emit('load-more')
  }
}

const getChapterStatusType = (status) => {
  const statusMap = {
    draft: 'warning',
    completed: 'success',
    published: 'primary'
  }
  return statusMap[status] || 'warning'
}

const getChapterStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    completed: '完成',
    published: '发表'
  }
  return statusMap[status] || '草稿'
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
  height: calc(100vh - 150px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}

.chapters-list {
  max-height: calc(100vh - 190px);
  overflow-y: auto;
  scroll-behavior: smooth;
}

.chapter-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.chapter-item.active {
  border-color: #409eff;
  background-color: #e6f4ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.chapter-info {
  flex: 1;
}

.chapter-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.chapter-info p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.chapter-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.chapter-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.3;
}

.chapter-desc-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.chapter-desc-truncated:hover {
  color: #606266;
}

.chapter-actions {
  display: flex;
  gap: 4px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #909399;
  font-size: 14px;
  gap: 8px;
}

.no-more-data {
  text-align: center;
  padding: 16px;
  color: #c0c4cc;
  font-size: 12px;
  border-top: 1px solid #f0f0f0;
}

.empty-chapters {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-chapters p {
  margin-bottom: 16px;
  font-size: 14px;
}
</style>
